<template>
  <div class="accident-report-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><Warning /></el-icon>
            {{ $t('accident.title') }}
          </h1>
          <p class="page-subtitle">{{ $t('accident.subtitle') }}</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createNewReport">
            <el-icon><Plus /></el-icon>
            {{ $t('accident.newReport') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalReports }}</div>
          <div class="stat-label">{{ $t('accident.totalReports') }}</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon draft">
          <el-icon><Edit /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ draftReports }}</div>
          <div class="stat-label">{{ $t('accident.draft') }}</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon submitted">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ submittedReports }}</div>
          <div class="stat-label">{{ $t('accident.submitted') }}</div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <div class="filter-content">
          <el-select
            v-model="selectedStatus"
            :placeholder="$t('accident.selectStatus')"
            clearable
            @change="handleStatusChange"
            style="width: 150px"
          >
            <el-option :label="$t('accident.draft')" value="draft" />
            <el-option :label="$t('accident.submitted')" value="submitted" />
            <el-option :label="$t('accident.archived')" value="archived" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            :range-separator="$t('accident.dateTo')"
            :start-placeholder="$t('accident.startDate')"
            :end-placeholder="$t('accident.endDate')"
            @change="handleDateChange"
            style="width: 240px"
          />
          <el-button @click="clearFilters">{{ $t('accident.clearFilters') }}</el-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="reports.length === 0" class="empty-state">
      <el-icon class="empty-icon"><Warning /></el-icon>
      <h3>{{ selectedStatus || dateRange ? $t('accident.noReportsFound') : $t('accident.noReports') }}</h3>
      <p>{{ selectedStatus || dateRange ? $t('accident.tryOtherFilters') : $t('accident.noReportsDesc') }}</p>
      <el-button type="primary" @click="createNewReport">
        {{ $t('accident.createReport') }}
      </el-button>
    </div>

    <!-- 报告列表 -->
    <div v-else class="reports-container">
      <div class="reports-grid">
        <div
          v-for="report in reports"
          :key="report.id"
          class="report-card"
          @click="viewReport(report)"
        >
          <div class="report-header">
            <div class="report-status">
              <el-tag
                :type="getStatusType(report.status)"
                size="small"
              >
                {{ getStatusText(report.status) }}
              </el-tag>
            </div>
            <div class="report-date">
              {{ formatDate(report.accidentTime) }}
            </div>
          </div>

          <div class="report-content">
            <div class="report-location">
              <el-icon><Location /></el-icon>
              <span>{{ report.accidentLocation || $t('accident.noLocation') }}</span>
            </div>
            <div class="report-description">
              {{ report.description || $t('accident.noDescription') }}
            </div>
          </div>

          <div class="report-footer">
            <div class="report-info">
              <span class="photo-count" v-if="report.photoCount > 0">
                <el-icon><Picture /></el-icon>
                {{ $t('accident.photoCount', { count: report.photoCount }) }}
              </span>
              <span class="created-time">
                {{ $t('accident.createdAt') }} {{ formatDate(report.createdAt) }}
              </span>
            </div>
            <div class="report-actions">
              <el-button
                size="small"
                @click.stop="editReport(report)"
              >
                <el-icon><Edit /></el-icon>
                {{ $t('common.edit') }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click.stop="deleteReport(report)"
              >
                <el-icon><Delete /></el-icon>
                {{ $t('common.delete') }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48]"
          :total="totalReports"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑报告对话框 -->
    <el-dialog
      v-model="reportDialogVisible"
      :title="isEditing ? $t('accident.editReport') : $t('accident.createReport')"
      :width="isMobile ? '100%' : '700px'"
      :fullscreen="isMobile"
      :before-close="handleCloseReportDialog"
      class="accident-report-dialog"
      :class="{ 'mobile-dialog': isMobile }"
    >
      <el-form
        ref="reportForm"
        :model="reportFormData"
        :rules="reportRules"
        :label-width="isMobile ? '80px' : '100px'"
        :label-position="isMobile ? 'top' : 'right'"
        class="accident-form"
        :class="{ 'mobile-form': isMobile }"
      >
        <el-form-item :label="$t('accident.accidentTime')" prop="accidentTime">
          <el-date-picker
            v-model="reportFormData.accidentTime"
            type="datetime"
            :placeholder="$t('accident.selectAccidentTime')"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item :label="$t('accident.accidentLocation')" prop="accidentLocation">
          <el-input
            v-model="reportFormData.accidentLocation"
            :placeholder="$t('accident.enterAccidentLocation')"
          />
        </el-form-item>

        <el-form-item :label="$t('accident.accidentDescription')" prop="description">
          <el-input
            v-model="reportFormData.description"
            type="textarea"
            :rows="4"
            :placeholder="$t('accident.enterAccidentDescription')"
          />
        </el-form-item>

        <el-form-item :label="$t('accident.otherPartyInfo')" prop="otherPartyInfo">
          <el-input
            v-model="reportFormData.otherPartyInfo"
            type="textarea"
            :rows="3"
            :placeholder="$t('accident.enterOtherPartyInfo')"
          />
        </el-form-item>

        <el-form-item :label="$t('accident.scenePhotos')">
          <div class="photo-upload-section">
            <div class="photo-categories">
              <!-- 现场照片 -->
              <div class="category-section">
                <div class="category-header">
                  <el-icon><Picture /></el-icon>
                  <span>现场照片</span>
                  <el-tag size="small" type="info">{{ photoCategories.scene.length }}/3</el-tag>
                </div>
                <div class="category-upload">
                  <el-upload
                    :auto-upload="false"
                    :file-list="photoCategories.scene"
                    :on-change="(file, fileList) => handleCategoryPhotoChange('scene', file, fileList)"
                    :on-remove="(file, fileList) => handleCategoryPhotoRemove('scene', file, fileList)"
                    :before-upload="beforePhotoUpload"
                    accept="image/*"
                    multiple
                    list-type="picture-card"
                    :limit="3"
                    class="category-uploader"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
                <div v-if="photoCategories.scene.length > 0" class="category-captions">
                  <div v-for="(photo, index) in photoCategories.scene" :key="photo.uid" class="caption-item">
                    <el-input
                      v-model="photo.caption"
                      placeholder="添加说明..."
                      size="small"
                      maxlength="100"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>

              <!-- 车辆前方 -->
              <div class="category-section">
                <div class="category-header">
                  <el-icon><Picture /></el-icon>
                  <span>车辆前方</span>
                  <el-tag size="small" type="info">{{ photoCategories.front.length }}/2</el-tag>
                </div>
                <div class="category-upload">
                  <el-upload
                    :auto-upload="false"
                    :file-list="photoCategories.front"
                    :on-change="(file, fileList) => handleCategoryPhotoChange('front', file, fileList)"
                    :on-remove="(file, fileList) => handleCategoryPhotoRemove('front', file, fileList)"
                    :before-upload="beforePhotoUpload"
                    accept="image/*"
                    multiple
                    list-type="picture-card"
                    :limit="2"
                    class="category-uploader"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
                <div v-if="photoCategories.front.length > 0" class="category-captions">
                  <div v-for="(photo, index) in photoCategories.front" :key="photo.uid" class="caption-item">
                    <el-input
                      v-model="photo.caption"
                      placeholder="添加说明..."
                      size="small"
                      maxlength="100"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>

              <!-- 车辆侧面 -->
              <div class="category-section">
                <div class="category-header">
                  <el-icon><Picture /></el-icon>
                  <span>车辆侧面</span>
                  <el-tag size="small" type="info">{{ photoCategories.side.length }}/2</el-tag>
                </div>
                <div class="category-upload">
                  <el-upload
                    :auto-upload="false"
                    :file-list="photoCategories.side"
                    :on-change="(file, fileList) => handleCategoryPhotoChange('side', file, fileList)"
                    :on-remove="(file, fileList) => handleCategoryPhotoRemove('side', file, fileList)"
                    :before-upload="beforePhotoUpload"
                    accept="image/*"
                    multiple
                    list-type="picture-card"
                    :limit="2"
                    class="category-uploader"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
                <div v-if="photoCategories.side.length > 0" class="category-captions">
                  <div v-for="(photo, index) in photoCategories.side" :key="photo.uid" class="caption-item">
                    <el-input
                      v-model="photo.caption"
                      placeholder="添加说明..."
                      size="small"
                      maxlength="100"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>

              <!-- 车辆后方 -->
              <div class="category-section">
                <div class="category-header">
                  <el-icon><Picture /></el-icon>
                  <span>车辆后方</span>
                  <el-tag size="small" type="info">{{ photoCategories.rear.length }}/2</el-tag>
                </div>
                <div class="category-upload">
                  <el-upload
                    :auto-upload="false"
                    :file-list="photoCategories.rear"
                    :on-change="(file, fileList) => handleCategoryPhotoChange('rear', file, fileList)"
                    :on-remove="(file, fileList) => handleCategoryPhotoRemove('rear', file, fileList)"
                    :before-upload="beforePhotoUpload"
                    accept="image/*"
                    multiple
                    list-type="picture-card"
                    :limit="2"
                    class="category-uploader"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
                <div v-if="photoCategories.rear.length > 0" class="category-captions">
                  <div v-for="(photo, index) in photoCategories.rear" :key="photo.uid" class="caption-item">
                    <el-input
                      v-model="photo.caption"
                      placeholder="添加说明..."
                      size="small"
                      maxlength="100"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>

              <!-- 局部细节 -->
              <div class="category-section">
                <div class="category-header">
                  <el-icon><Picture /></el-icon>
                  <span>局部细节</span>
                  <el-tag size="small" type="info">{{ photoCategories.detail.length }}/3</el-tag>
                </div>
                <div class="category-upload">
                  <el-upload
                    :auto-upload="false"
                    :file-list="photoCategories.detail"
                    :on-change="(file, fileList) => handleCategoryPhotoChange('detail', file, fileList)"
                    :on-remove="(file, fileList) => handleCategoryPhotoRemove('detail', file, fileList)"
                    :before-upload="beforePhotoUpload"
                    accept="image/*"
                    multiple
                    list-type="picture-card"
                    :limit="3"
                    class="category-uploader"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
                <div v-if="photoCategories.detail.length > 0" class="category-captions">
                  <div v-for="(photo, index) in photoCategories.detail" :key="photo.uid" class="caption-item">
                    <el-input
                      v-model="photo.caption"
                      placeholder="添加说明..."
                      size="small"
                      maxlength="100"
                      show-word-limit
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 照片提示 -->
            <div v-if="getTotalPhotoCount() > 0" class="photo-summary">
              <el-alert
                :title="`已选择 ${getTotalPhotoCount()} 张照片，将在保存报告时一起上传`"
                type="info"
                :closable="false"
                show-icon
              />
            </div>

            <div class="upload-tip">
              <strong>使用说明：</strong>
              <br>• 现场照片：记录事故现场整体情况，包括道路状况、交通标志等
              <br>• 车辆各角度：记录车辆前方、侧面、后方的损伤情况
              <br>• 局部细节：记录具体损伤的特写照片
              <br>• 照片将在保存报告时自动上传，无需单独上传
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer" :class="{ 'mobile-footer': isMobile }">
          <el-button @click="reportDialogVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button @click="saveDraft" :loading="saving">
            <el-icon><Document /></el-icon>
            {{ isEditing ? '保存修改' : '保存草稿' }}
            <span v-if="getTotalPhotoCount() > 0" class="photo-count">
              (含{{ getTotalPhotoCount() }}张照片)
            </span>
          </el-button>
          <el-button type="primary" @click="submitReport" :loading="saving">
            <el-icon><Check /></el-icon>
            {{ isEditing ? '更新并提交' : '提交报告' }}
            <span v-if="getTotalPhotoCount() > 0" class="photo-count">
              (含{{ getTotalPhotoCount() }}张照片)
            </span>
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="$t('accident.reportDetailTitle', { location: selectedReport?.accidentLocation || $t('accident.unknownLocation') })"
      :width="isMobile ? '100%' : '800px'"
      :fullscreen="isMobile"
      :before-close="handleCloseDetailDialog"
      class="accident-detail-dialog"
      :class="{ 'mobile-dialog': isMobile }"
    >
      <div v-if="selectedReport" class="report-detail-content">
        <div class="detail-section">
          <h4>{{ $t('accident.basicInfo') }}</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>{{ $t('accident.accidentTime') }}:</label>
              <span>{{ formatDate(selectedReport.accidentTime) }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('accident.accidentLocation') }}:</label>
              <span>{{ selectedReport.accidentLocation || $t('accident.notSpecified') }}</span>
            </div>
            <div class="detail-item">
              <label>{{ $t('accident.reportStatus') }}:</label>
              <el-tag :type="getStatusType(selectedReport.status)" size="small">
                {{ getStatusText(selectedReport.status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>{{ $t('accident.createdAt') }}:</label>
              <span>{{ formatDate(selectedReport.createdAt) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedReport.description">
          <h4>{{ $t('accident.accidentDescription') }}</h4>
          <p class="description-text">{{ selectedReport.description }}</p>
        </div>

        <div class="detail-section" v-if="selectedReport.otherPartyInfo">
          <h4>{{ $t('accident.otherPartyInfo') }}</h4>
          <p class="description-text">{{ selectedReport.otherPartyInfo }}</p>
        </div>

        <div class="detail-section" v-if="reportPhotos.length > 0">
          <h4>{{ $t('accident.scenePhotos') }}</h4>
          <div class="photos-gallery">
            <div
              v-for="photo in reportPhotos"
              :key="photo.id"
              class="photo-item"
              @click="previewPhoto(photo)"
            >
              <img :src="photo.imageUrl" :alt="photo.caption" />
              <div class="photo-overlay">
                <el-icon><ZoomIn /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer" :class="{ 'mobile-footer': isMobile }">
          <el-button @click="detailDialogVisible = false">{{ $t('common.close') }}</el-button>
          <el-button @click="editReport(selectedReport)">{{ $t('accident.editReport') }}</el-button>
          <el-button
            type="success"
            @click="generatePDF"
            :loading="generatingPDF"
          >
            <el-icon><Document /></el-icon>
            生成PDF
          </el-button>
          <el-button v-if="selectedReport?.pdfUrl" type="primary" @click="downloadPDF">
            <el-icon><Download /></el-icon>
            {{ $t('accident.downloadPDF') }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="previewVisible"
      :url-list="previewUrls"
      :initial-index="previewIndex"
      @close="previewVisible = false"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/api'
import {
  Warning,
  Plus,
  Document,
  Edit,
  Check,
  Location,
  Picture,
  Delete,
  ZoomIn,
  Download,
  Upload
} from '@element-plus/icons-vue'

export default {
  name: 'AccidentReport',
  components: {
    Warning,
    Plus,
    Document,
    Edit,
    Check,
    Location,
    Picture,
    Delete,
    ZoomIn,
    Download,
    Upload
  },
  setup() {
    const store = useStore()
    const { t } = useI18n()

    const loading = ref(false)
    const saving = ref(false)
    const reports = ref([])
    const selectedStatus = ref('')
    const dateRange = ref([])
    const currentPage = ref(1)
    const pageSize = ref(12)
    const totalReports = ref(0)

    const reportDialogVisible = ref(false)
    const detailDialogVisible = ref(false)
    const isEditing = ref(false)
    const selectedReport = ref(null)
    const reportPhotos = ref([])

    const previewVisible = ref(false)
    const previewUrls = ref([])
    const previewIndex = ref(0)

    const photoList = ref([])
    const reportForm = ref(null)
    const uploading = ref(false)
    const generatingPDF = ref(false)

    // 照片分类管理
    const photoCategories = reactive({
      scene: [],    // 现场照片
      front: [],    // 车辆前方
      side: [],     // 车辆侧面
      rear: [],     // 车辆后方
      detail: []    // 局部细节
    })

    // 移动端检测
    const windowWidth = ref(window.innerWidth)
    const isMobile = computed(() => {
      return windowWidth.value <= 768
    })

    // 监听窗口大小变化
    const handleResize = () => {
      windowWidth.value = window.innerWidth
    }

    onMounted(() => {
      window.addEventListener('resize', handleResize)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })

    const reportFormData = reactive({
      accidentTime: null,
      accidentLocation: '',
      description: '',
      otherPartyInfo: ''
    })

    const reportRules = computed(() => ({
      accidentTime: [
        { required: true, message: t('accident.pleaseSelectAccidentTime'), trigger: 'change' }
      ],
      accidentLocation: [
        { required: true, message: t('accident.pleaseEnterAccidentLocation'), trigger: 'blur' }
      ]
    }))

    // 计算统计数据
    const draftReports = computed(() => {
      return reports.value.filter(r => r.status === 'draft').length
    })

    const submittedReports = computed(() => {
      return reports.value.filter(r => r.status === 'submitted').length
    })

    // 上传配置
    const uploadUrl = computed(() => {
      return `${import.meta.env.VITE_API_BASE_URL}/accidents/${selectedReport.value?.id || 'temp'}/photos`
    })

    const uploadHeaders = computed(() => {
      const token = store.getters['auth/token']
      return token ? { Authorization: `Bearer ${token}` } : {}
    })

    const uploadData = computed(() => ({
      photoType: 'scene',
      caption: ''
    }))

    // 获取事故报告列表
    const fetchReports = async () => {
      try {
        loading.value = true
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        }

        if (selectedStatus.value) {
          params.status = selectedStatus.value
        }

        if (dateRange.value && dateRange.value.length === 2) {
          params.startDate = dateRange.value[0].toISOString()
          params.endDate = dateRange.value[1].toISOString()
        }

        const response = await api.accidents.getAll(params)
        reports.value = response.data.reports
        totalReports.value = response.data.pagination.total
      } catch (error) {
        console.error('Error:', error)
        ElMessage.error(t('accident.fetchReportsFailed'))
      } finally {
        loading.value = false
      }
    }

    // 获取报告详情
    const fetchReportDetail = async (reportId) => {
      try {
        const response = await api.accidents.getById(reportId)
        selectedReport.value = response.data
        reportPhotos.value = response.data.photos || []
      } catch (error) {
        console.error('Error:', error)
        ElMessage.error(t('accident.fetchReportDetailFailed'))
      }
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 获取状态类型
    const getStatusType = (status) => {
      const typeMap = {
        'draft': 'info',
        'submitted': 'success',
        'archived': 'warning'
      }
      return typeMap[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const textMap = {
        'draft': t('accident.draft'),
        'submitted': t('accident.submitted'),
        'archived': t('accident.archived')
      }
      return textMap[status] || t('accident.unknown')
    }

    // 筛选处理
    const handleStatusChange = () => {
      currentPage.value = 1
      fetchReports()
    }

    const handleDateChange = () => {
      currentPage.value = 1
      fetchReports()
    }

    const clearFilters = () => {
      selectedStatus.value = ''
      dateRange.value = []
      currentPage.value = 1
      fetchReports()
    }

    // 分页处理
    const handleSizeChange = (newSize) => {
      pageSize.value = newSize
      currentPage.value = 1
      fetchReports()
    }

    const handleCurrentChange = (newPage) => {
      currentPage.value = newPage
      fetchReports()
    }

    // 创建新报告
    const createNewReport = () => {
      isEditing.value = false
      selectedReport.value = null
      Object.assign(reportFormData, {
        accidentTime: null,
        accidentLocation: '',
        description: '',
        otherPartyInfo: ''
      })
      photoList.value = []
      reportDialogVisible.value = true
    }

    // 编辑报告
    const editReport = async (report) => {
      isEditing.value = true
      selectedReport.value = report

      Object.assign(reportFormData, {
        accidentTime: new Date(report.accidentTime),
        accidentLocation: report.accidentLocation || '',
        description: report.description || '',
        otherPartyInfo: report.otherPartyInfo || ''
      })

      // 获取照片列表
      await fetchReportDetail(report.id)
      photoList.value = reportPhotos.value.map(photo => ({
        name: photo.caption || 'photo',
        url: photo.imageUrl,
        uid: photo.id
      }))

      reportDialogVisible.value = true
      detailDialogVisible.value = false
    }

    // 查看报告
    const viewReport = async (report) => {
      await fetchReportDetail(report.id)
      detailDialogVisible.value = true
    }

    // 删除报告
    const deleteReport = async (report) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除事故报告"${report.accidentLocation || '未知地点'}"吗？`,
          '确认删除',
          {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: 'warning'
          }
        )

        await api.accidents.delete(report.id)
        ElMessage.success(t('messages.success'))

        // 从列表中移除
        const index = reports.value.findIndex(r => r.id === report.id)
        if (index > -1) {
          reports.value.splice(index, 1)
          totalReports.value--
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Error:', error)
          ElMessage.error(t('messages.error'))
        }
      }
    }

    // 保存草稿
    const saveDraft = async () => {
      if (!reportForm.value) return

      try {
        const valid = await reportForm.value.validate()
        if (!valid) return

        saving.value = true

        const data = {
          ...reportFormData,
          status: 'draft'
        }

        let reportId
        if (isEditing.value && selectedReport.value) {
          await api.accidents.update(selectedReport.value.id, data)
          reportId = selectedReport.value.id
        } else {
          const response = await api.accidents.create(data)
          reportId = response.data.reportId
          selectedReport.value = { id: reportId }
        }

        // 如果有照片，上传照片
        const totalPhotos = getTotalPhotoCount()
        if (totalPhotos > 0) {
          await uploadPhotosToReport(reportId)
        }

        ElMessage.success('草稿保存成功')
        reportDialogVisible.value = false
        fetchReports()
      } catch (error) {
        console.error('Error:', error)
        ElMessage.error('保存草稿失败')
      } finally {
        saving.value = false
      }
    }

    // 提交报告
    const submitReport = async () => {
      if (!reportForm.value) return

      try {
        const valid = await reportForm.value.validate()
        if (!valid) return

        saving.value = true

        const data = {
          ...reportFormData,
          status: 'submitted'
        }

        let reportId
        if (isEditing.value && selectedReport.value) {
          await api.accidents.update(selectedReport.value.id, data)
          reportId = selectedReport.value.id
        } else {
          const response = await api.accidents.create(data)
          reportId = response.data.reportId
          selectedReport.value = { id: reportId }
        }

        // 如果有照片，上传照片
        const totalPhotos = getTotalPhotoCount()
        if (totalPhotos > 0) {
          await uploadPhotosToReport(reportId)
        }

        ElMessage.success('报告提交成功')
        reportDialogVisible.value = false
        fetchReports()
      } catch (error) {
        console.error('Error:', error)
        ElMessage.error('提交报告失败')
      } finally {
        saving.value = false
      }
    }

    // 照片上传处理
    const beforePhotoUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage) {
        ElMessage.error('请选择图片文件')
        return false
      }
      if (!isLt10M) {
        ElMessage.error('图片大小不能超过10MB')
        return false
      }
      return true
    }

    // 分类照片变化处理
    const handleCategoryPhotoChange = (category, file, fileList) => {
      // 为新添加的照片设置默认说明
      if (file.status === 'ready') {
        file.caption = ''
      }
      photoCategories[category] = fileList
    }

    const handleCategoryPhotoRemove = (category, file, fileList) => {
      photoCategories[category] = fileList
    }

    // 获取总照片数量
    const getTotalPhotoCount = () => {
      return Object.values(photoCategories).reduce((total, photos) => total + photos.length, 0)
    }

    // 上传照片到指定报告
    const uploadPhotosToReport = async (reportId) => {
      const totalPhotos = getTotalPhotoCount()
      if (totalPhotos === 0) {
        return // 没有照片就不上传
      }

      const formData = new FormData()
      const photoTypes = []
      const captions = []

      // 按分类添加文件和对应的元数据
      Object.entries(photoCategories).forEach(([category, photos]) => {
        photos.forEach((photo) => {
          if (photo.raw) {
            formData.append('photos', photo.raw)
            photoTypes.push(category)
            captions.push(photo.caption || '')
          }
        })
      })

      // 添加数组形式的元数据
      photoTypes.forEach(type => formData.append('photoTypes', type))
      captions.forEach(caption => formData.append('captions', caption))

      const response = await api.accidents.uploadPhotos(reportId, formData)

      if (response.success) {
        // 清空所有分类的照片
        Object.keys(photoCategories).forEach(category => {
          photoCategories[category] = []
        })
        return response
      } else {
        throw new Error('照片上传失败')
      }
    }

    // 清空所有照片
    const clearAllPhotos = () => {
      Object.keys(photoCategories).forEach(category => {
        photoCategories[category] = []
      })
    }

    // 预览照片
    const previewPhoto = (photo) => {
      previewUrls.value = reportPhotos.value.map(p => p.imageUrl)
      previewIndex.value = reportPhotos.value.findIndex(p => p.id === photo.id)
      previewVisible.value = true
    }

    // 生成PDF
    const generatePDF = async () => {
      if (!selectedReport.value?.id) {
        ElMessage.error('无效的报告')
        return
      }

      try {
        generatingPDF.value = true
        const response = await api.accidents.generatePdf(selectedReport.value.id)

        if (response.success) {
          ElMessage.success('PDF生成成功')
          // 更新报告的PDF URL
          selectedReport.value.pdfUrl = response.data.pdfUrl
          // 自动下载PDF
          window.open(response.data.pdfUrl, '_blank')
        }
      } catch (error) {
        console.error('Error:', error)
        ElMessage.error('PDF生成失败')
      } finally {
        generatingPDF.value = false
      }
    }

    // 下载PDF
    const downloadPDF = () => {
      if (selectedReport.value?.pdfUrl) {
        window.open(selectedReport.value.pdfUrl, '_blank')
      } else {
        ElMessage.info('PDF文件不存在，请先生成PDF')
      }
    }

    // 关闭对话框
    const handleCloseReportDialog = () => {
      reportDialogVisible.value = false
    }

    const handleCloseDetailDialog = () => {
      detailDialogVisible.value = false
      selectedReport.value = null
      reportPhotos.value = []
    }

    onMounted(() => {
      fetchReports()
    })

    return {
      loading,
      saving,
      reports,
      selectedStatus,
      dateRange,
      currentPage,
      pageSize,
      totalReports,
      draftReports,
      submittedReports,
      reportDialogVisible,
      detailDialogVisible,
      isEditing,
      selectedReport,
      reportPhotos,
      previewVisible,
      previewUrls,
      previewIndex,
      photoList,
      reportForm,
      reportFormData,
      reportRules,
      uploading,
      generatingPDF,
      isMobile,
      photoCategories,
      uploadUrl,
      uploadHeaders,
      uploadData,
      formatDate,
      getStatusType,
      getStatusText,
      handleStatusChange,
      handleDateChange,
      clearFilters,
      handleSizeChange,
      handleCurrentChange,
      createNewReport,
      editReport,
      viewReport,
      deleteReport,
      saveDraft,
      submitReport,
      beforePhotoUpload,
      handleCategoryPhotoChange,
      handleCategoryPhotoRemove,
      getTotalPhotoCount,
      uploadPhotosToReport,
      clearAllPhotos,
      previewPhoto,
      generatePDF,
      downloadPDF,
      handleCloseReportDialog,
      handleCloseDetailDialog,
      // 图标
      Warning,
      Plus,
      Document,
      Edit,
      Check,
      Location,
      Picture,
      Delete,
      ZoomIn,
      Download,
      Upload
    }
  }
}
</script>

<style lang="scss" scoped>
.accident-report-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .page-subtitle {
    font-size: 16px;
    color: var(--el-text-color-regular);
  }
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;

  &.total { background: var(--el-color-warning); }
  &.draft { background: var(--el-color-info); }
  &.submitted { background: var(--el-color-success); }
}

.stat-content {
  .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    line-height: 1;
  }

  .stat-label {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-top: 4px;
  }
}

.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.loading-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .empty-icon {
    font-size: 64px;
    color: var(--el-text-color-placeholder);
    margin-bottom: 20px;
  }

  h3 {
    color: var(--el-text-color-primary);
    margin-bottom: 10px;
  }

  p {
    color: var(--el-text-color-regular);
    margin-bottom: 20px;
  }
}

.reports-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.report-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.report-date {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.report-content {
  margin-bottom: 16px;
}

.report-location {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.report-description {
  color: var(--el-text-color-regular);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.report-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.report-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.photo-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-color-primary);
}

.created-time {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.report-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.photo-upload-section {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: var(--el-text-color-placeholder);
  }
}

.report-detail-content {
  .detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }
  }

  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .detail-item {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      min-width: 80px;
    }

    span {
      color: var(--el-text-color-primary);
    }
  }

  .description-text {
    line-height: 1.6;
    color: var(--el-text-color-regular);
    background: var(--el-fill-color-lighter);
    padding: 16px;
    border-radius: 6px;
  }
}

.photos-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s;

  &:hover {
    transform: scale(1.05);

    .photo-overlay {
      opacity: 1;
    }
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  opacity: 0;
  transition: opacity 0.3s;
}

/* 移动端对话框优化 */
.mobile-dialog {
  :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100vh;
    border-radius: 0;

    .el-dialog__header {
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
      }

      .el-dialog__headerbtn {
        top: 16px;
        right: 16px;
        width: 32px;
        height: 32px;

        .el-dialog__close {
          font-size: 18px;
        }
      }
    }

    .el-dialog__body {
      padding: 20px;
      max-height: calc(100vh - 140px);
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;
      background: #fafafa;
    }
  }
}

/* 对话框footer优化 */
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.mobile-footer {
  flex-direction: column;
  gap: 8px;

  .el-button {
    width: 100%;
    margin: 0;
    order: 2;

    &.el-button--primary {
      order: 1;
    }

    &.el-button--success {
      order: 1;
    }
  }
}

/* 按钮照片计数样式 */
.photo-count {
  font-size: 11px;
  opacity: 0.8;
  margin-left: 4px;
}

/* 移动端表单优化 */
.mobile-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .el-input,
    .el-textarea,
    .el-date-editor,
    .el-select {
      width: 100%;
    }

    .el-textarea .el-textarea__inner {
      min-height: 80px;
    }
  }
}

/* 移动端上传区域优化 */
.mobile-upload {
  .mobile-uploader {
    :deep(.el-upload-list--picture-card) {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .el-upload-list__item {
        width: 70px;
        height: 70px;
        margin: 0;
        border-radius: 6px;
      }
    }

    :deep(.el-upload--picture-card) {
      width: 70px;
      height: 70px;
      border-radius: 6px;

      .upload-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }

      .upload-text {
        font-size: 10px;
      }
    }
  }
}

/* 移动端照片分类优化 */
@media (max-width: 768px) {
  .photo-upload-section {
    .category-section {
      padding: 12px;

      .category-header {
        font-size: 14px;
        margin-bottom: 8px;
      }

      .category-upload {
        .category-uploader {
          :deep(.el-upload--picture-card) {
            width: 60px;
            height: 60px;

            .el-icon {
              font-size: 16px;
            }
          }

          :deep(.el-upload-list--picture-card) {
            .el-upload-list__item {
              width: 60px;
              height: 60px;
              margin: 0 6px 6px 0;
            }
          }
        }
      }

      .category-captions {
        .caption-item {
          .el-input {
            :deep(.el-input__inner) {
              font-size: 12px;
            }
          }
        }
      }
    }

    .upload-tip {
      font-size: 11px;
      padding: 8px;
    }
  }
}

@media (max-width: 768px) {
  .accident-report-page {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }

  .reports-grid {
    grid-template-columns: 1fr;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .photos-gallery {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .report-footer {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 照片分类上传样式 */
.photo-upload-section {
  .photo-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  .category-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e4e7ed;

    .category-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 600;
      color: #303133;

      .el-icon {
        color: #409eff;
      }

      span {
        flex: 1;
      }

      .el-tag {
        font-size: 11px;
      }
    }

    .category-upload {
      margin-bottom: 12px;

      .category-uploader {
        :deep(.el-upload--picture-card) {
          width: 80px;
          height: 80px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
            background-color: #f0f9ff;
          }

          .el-icon {
            font-size: 20px;
            color: #8c939d;
          }
        }

        :deep(.el-upload-list--picture-card) {
          .el-upload-list__item {
            width: 80px;
            height: 80px;
            margin: 0 8px 8px 0;
            border-radius: 6px;
          }
        }
      }
    }

    .category-captions {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .caption-item {
        .el-input {
          :deep(.el-input__wrapper) {
            border-radius: 4px;
          }
        }
      }
    }
  }

  .upload-actions {
    margin-bottom: 16px;

    .el-button {
      border-radius: 8px;
      font-weight: 600;
    }
  }

  .photo-summary {
    margin-bottom: 16px;

    .el-alert {
      border-radius: 6px;
    }
  }

  .upload-tip {
    font-size: 12px;
    color: #606266;
    line-height: 1.6;
    background: #f0f9ff;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #409eff;

    strong {
      color: #303133;
      font-weight: 600;
    }
  }
}
</style>
